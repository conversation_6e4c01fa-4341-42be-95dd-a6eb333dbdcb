// Debug script to test timer functionality in browser console
// Run this in the browser console to test the autostart fix

console.log('🔧 Timer Debug Script Loaded');

// Helper function to wait for a condition
function waitFor(condition, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const check = () => {
      if (condition()) {
        resolve();
      } else if (Date.now() - startTime > timeout) {
        reject(new Error('Timeout waiting for condition'));
      } else {
        setTimeout(check, 100);
      }
    };
    check();
  });
}

// Helper function to get timer state
function getTimerState() {
  const timerDisplay = document.querySelector('[data-testid="timer-display"]') || 
                      document.querySelector('.text-6xl, .text-5xl, .text-4xl');
  const modeDisplay = document.querySelector('[data-testid="mode-display"]') ||
                     document.querySelector('h1, h2');
  
  return {
    time: timerDisplay?.textContent || 'Unknown',
    mode: modeDisplay?.textContent || 'Unknown',
    timestamp: new Date().toISOString()
  };
}

// Helper function to click autostart toggle
function enableAutostart() {
  const toggle = document.querySelector('[id="auto-start"]');
  if (toggle && !toggle.checked) {
    toggle.click();
    console.log('✅ Autostart enabled');
  } else if (toggle?.checked) {
    console.log('ℹ️ Autostart already enabled');
  } else {
    console.log('❌ Could not find autostart toggle');
  }
}

// Helper function to start timer
function startTimer() {
  const startButton = document.querySelector('button[aria-label*="Start"], button:has(svg[data-lucide="play"])');
  if (startButton && !startButton.disabled) {
    startButton.click();
    console.log('▶️ Timer started');
    return true;
  } else {
    console.log('❌ Could not start timer or timer already running');
    return false;
  }
}

// Main test function
async function testAutostart() {
  console.log('🚀 Starting autostart test...');
  
  try {
    // Enable autostart
    enableAutostart();
    
    // Log initial state
    console.log('📊 Initial state:', getTimerState());
    
    // Start timer
    if (!startTimer()) {
      throw new Error('Could not start timer');
    }
    
    // Monitor timer for transitions
    let lastState = getTimerState();
    let transitionCount = 0;
    const maxTransitions = 3; // Focus -> Break -> Focus
    
    console.log('👀 Monitoring timer transitions...');
    
    const monitorInterval = setInterval(() => {
      const currentState = getTimerState();
      
      // Check if mode changed (transition occurred)
      if (currentState.mode !== lastState.mode) {
        transitionCount++;
        console.log(`🔄 Transition ${transitionCount}: ${lastState.mode} -> ${currentState.mode}`);
        console.log(`⏰ New timer value: ${currentState.time}`);
        
        // Check if timer shows 0:00 (the bug we're fixing)
        if (currentState.time.includes('0:00') || currentState.time.includes('00:00')) {
          console.error('❌ BUG DETECTED: Timer shows 0:00 after transition!');
        } else {
          console.log('✅ Timer shows correct duration after transition');
        }
        
        lastState = currentState;
        
        if (transitionCount >= maxTransitions) {
          clearInterval(monitorInterval);
          console.log('🎉 Test completed successfully!');
        }
      }
    }, 1000);
    
    // Set timeout to stop monitoring after 10 minutes
    setTimeout(() => {
      clearInterval(monitorInterval);
      console.log('⏰ Test timeout reached');
    }, 600000);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Quick test function for immediate feedback
function quickTest() {
  console.log('⚡ Quick timer state check:');
  console.log(getTimerState());
  
  const autostart = document.querySelector('[id="auto-start"]');
  console.log('🔄 Autostart enabled:', autostart?.checked || false);
}

// Export functions to global scope for easy access
window.testAutostart = testAutostart;
window.quickTest = quickTest;
window.getTimerState = getTimerState;
window.enableAutostart = enableAutostart;
window.startTimer = startTimer;

console.log('📋 Available functions:');
console.log('- testAutostart(): Run full autostart test');
console.log('- quickTest(): Check current timer state');
console.log('- getTimerState(): Get current timer state object');
console.log('- enableAutostart(): Enable autostart toggle');
console.log('- startTimer(): Start the timer');
