// Import the functions you need from the SDKs you need
import { initializeApp, getApp, type FirebaseOptions } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import { getAuth, signInAnonymously ,onAuthStateChanged} from 'firebase/auth';
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
const firebaseConfig: FirebaseOptions = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

function getAnonymousAuth(app: any) {
  const auth = getAuth(app);
  onAuthStateChanged(auth, (user) => {
    if (!user) {
      signInAnonymously(auth)
        .catch((error) => {
          console.error("Error signing in anonymously:", error.code, error.message);
        });
    }
  });
  return auth;
}

function createFirebaseApp(config: FirebaseOptions) {
  try {
    return getApp();
  } catch {
    const app = initializeApp(config);
    // console.log(`
    //   *** Firebase Console Action Required ***
    //   Please update your Realtime Database rules in the Firebase console to allow session name creation.
    //   Go to your Firebase project -> Realtime Database -> Rules and paste the following:
    //   {
    //     "rules": {
    //       "sessions": {
    //         "$uid": {
    //           ".read": true,
    //           ".write": "$uid === auth.uid",
    //           "call": {
    //             // Allow anyone to initiate a call, but only the session owner can respond
    //             ".write": "auth != null",
    //             "status": {
    //               ".validate": "newData.isString() && (newData.val() === 'calling' || newData.val() === 'busy' || newData.val() === 'ok' || newData.val() === 'no_response')"
    //             },
    //             "timestamp": {
    //                ".validate": "newData.isNumber()"
    //             },
    //             "$other": { ".validate": false }
    //           }
    //         }
    //       },
    //       "sessionNames": {
    //         ".read": true,
    //         "$name": {
    //           ".write": "!data.exists() && auth != null",
    //           "sessionId": {
    //             ".validate": "newData.isString()"
    //           },
    //           "$other": { ".validate": false }
    //         }
    //       }
    //     }
    //   }
    // `);
    return app;
  }
}

// Initialize Firebase
export const firebaseApp = createFirebaseApp(firebaseConfig);
export const database = getDatabase(firebaseApp);
export const auth = getAnonymousAuth(firebaseApp);
