'use client';

import { Settings, Languages, Maximize, Minimize, BellRing } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SettingsSheet } from './settings-sheet';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage } from '@/providers/language-provider';
import { UsaFlag } from './icons/usa-flag';
import { SpainFlag } from './icons/spain-flag';
import { APP_NAME } from '@/lib/constants';
import { useFullscreen } from '@/providers/fullscreen-provider';
import { useTimer } from '@/providers/timer-provider';
import { CompanionLink } from './companion-link';
import { useSettings } from '@/providers/settings-provider';

export function AppHeader() {
  const { language, setLanguage } = useLanguage();
  const { isFullscreen, toggleFullscreen } = useFullscreen();
  const { sessionId, isRinging } = useTimer();
  const { settings } = useSettings();

  if (isFullscreen) {
    return (
        <header className="absolute top-0 right-0 p-4">
             {isRinging && (
                <div className="absolute top-4 left-4 p-2 bg-accent rounded-full animate-bounce">
                    <BellRing className="h-6 w-6 text-accent-foreground" />
                </div>
             )}
            <Button variant="outline" size="icon" onClick={toggleFullscreen} className="bg-background/50 hover:bg-background/80">
                <Minimize className="h-5 w-5" />
                <span className="sr-only">Exit Fullscreen</span>
            </Button>
        </header>
    )
  }

  return (
    <header className="p-4 flex justify-between items-center">
      <div className='flex items-center gap-2'>
        <h1 className="text-xl md:text-2xl font-bold text-foreground font-headline">{APP_NAME}</h1>
        {isRinging && (
            <div className="p-2 bg-accent rounded-full animate-bounce">
                <BellRing className="h-5 w-5 text-accent-foreground" />
            </div>
        )}
      </div>
      <div className="flex items-center gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="icon">
              <Languages className="h-5 w-5" />
              <span className="sr-only">Change language</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onSelect={() => setLanguage('en')}>
              <UsaFlag className="w-5 h-5 mr-2" />
              English
            </DropdownMenuItem>
            <DropdownMenuItem onSelect={() => setLanguage('es')}>
              <SpainFlag className="w-5 h-5 mr-2" />
              Español
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {settings.companionModeEnabled && sessionId && <CompanionLink sessionId={sessionId} />}

        <SettingsSheet>
          <Button variant="outline" size="icon">
            <Settings className="h-5 w-5" />
            <span className="sr-only">Settings</span>
          </Button>
        </SettingsSheet>

        <Button variant="outline" size="icon" onClick={toggleFullscreen}>
            <Maximize className="h-5 w-5" />
            <span className="sr-only">Enter Fullscreen</span>
        </Button>
      </div>
    </header>
  );
}
