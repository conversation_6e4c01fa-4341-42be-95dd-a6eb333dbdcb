'use client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, X } from 'lucide-react';
import {
  <PERSON><PERSON><PERSON><PERSON>og,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
} from './ui/alert-dialog';
import { Button } from './ui/button';

interface CallModalProps {
  open: boolean;
  onBusy: () => void;
  onOk: () => void;
}

export function CallModal({ open, onBusy, onOk }: CallModalProps) {
  return (
    <AlertDialog open={open}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100 dark:bg-yellow-900/50">
            <AlertTriangle className="h-8 w-8 text-yellow-500 dark:text-yellow-400 animate-pulse" />
          </div>
          <AlertDialogTitle className="text-center text-2xl font-bold tracking-tight">
            CALLING
          </AlertDialogTitle>
          <AlertDialogDescription className="text-center">
            A companion is trying to get your attention.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="grid grid-cols-2 gap-4">
          <Button variant="destructive" onClick={onBusy} size="lg">
            <X className="mr-2 h-5 w-5" />
            Busy
          </Button>
          <Button onClick={onOk} size="lg">
            <Check className="mr-2 h-5 w-5" />
            OK
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
