import { cn } from '@/lib/utils';

interface VintageClockProps {
  time: number;
  className?: string;
}

export function VintageClock({ time, className }: VintageClockProps) {
  const seconds = time % 60;
  const minutes = Math.floor(time / 60);
  
  const secondsDeg = (seconds / 60) * 360 + 90;
  const minutesDeg = (minutes / 60) * 360 + (seconds / 60) * 6 + 90;

  return (
    <div className={cn("relative w-48 h-48 md:w-64 md:h-64 rounded-full border-8 border-yellow-800/50 bg-[#f3e9d2] shadow-inner", className)}>
        {/* Face */}
        <div className="absolute inset-0 w-full h-full rounded-full bg-gradient-to-br from-[#f3e9d2] to-[#c8bda5]"></div>

        {/* Numbers */}
        {[...Array(12)].map((_, i) => {
            const num = i === 0 ? 12 : i;
            const angle = i * 30 * (Math.PI/180);
            const x = 50 + 40 * Math.sin(angle);
            const y = 50 - 40 * Math.cos(angle);
            return (
                <div key={i} className="absolute text-xl font-serif text-yellow-900/80" style={{ left: `${x}%`, top: `${y}%`, transform: 'translate(-50%, -50%)' }}>
                   {num}
                </div>
            )
        })}

        {/* Center dot */}
        <div className="absolute top-1/2 left-1/2 w-4 h-4 bg-yellow-900/70 rounded-full -translate-x-1/2 -translate-y-1/2 z-10 border-2 border-[#f3e9d2]"></div>

        {/* Hands */}
        <div className="absolute w-full h-full flex items-center justify-center">
            {/* Minute Hand */}
            <div
                className="absolute w-[35%] h-1.5 bg-yellow-900/80 rounded-sm origin-left"
                style={{ transform: `rotate(${minutesDeg}deg)` }}
            ></div>
            {/* Second Hand */}
            <div
                className="absolute w-[40%] h-0.5 bg-red-800/70 rounded-sm origin-left"
                style={{ transform: `rotate(${secondsDeg}deg)` }}
            ></div>
        </div>
    </div>
  );
}
