import type { SVGProps } from 'react';

export function PlusOneIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      {/* Plus symbol */}
      <path d="M8 8v8" />
      <path d="M4 12h8" />
      {/* Number "1" */}
      <path d="M16 8v8" />
      <path d="M14 10l2-2" />
      <path d="M14 16h4" />
    </svg>
  );
}

export function PlusFiveIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      {...props}
    >
      {/* Plus symbol */}
      <path d="M6 6v8" />
      <path d="M2 10h8" />
      {/* Number "5" */}
      <path d="M14 6h6" />
      <path d="M14 6v4h4c1 0 2 1 2 2s-1 2-2 2h-4" />
      <path d="M14 10h4" />
    </svg>
  );
}
