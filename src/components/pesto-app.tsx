'use client';

import { useEffect } from 'react';
import { useSettings } from '@/providers/settings-provider';
import { useTimer } from '@/providers/timer-provider';
import { AppHeader } from './app-header';
import { TimerDisplay } from './timer-display';
import { TimerControls } from './timer-controls';
import { useToast } from '@/hooks/use-toast';
import { Button } from './ui/button';

export function PestoApp() {
  const { settings, isLoaded } = useSettings();
  const { mode } = useTimer();
  const { toast } = useToast();

  const background = settings.backgrounds[mode] || settings.backgrounds.focus;
  
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js').then(registration => {
            registration.onupdatefound = () => {
                const installingWorker = registration.installing;
                if (installingWorker) {
                    installingWorker.onstatechange = () => {
                        if (installingWorker.state === 'installed' && navigator.serviceWorker.controller) {
                             // New content is available and has been installed.
                            toast({
                                title: "Update Available",
                                description: "A new version of the app is available.",
                                action: <Button onClick={() => window.location.reload()}>Reload</Button>,
                                duration: Infinity, // Persist until user action
                            });
                        }
                    };
                }
            };
        }).catch(err => {
          console.error('Service Worker registration failed: ', err);
        });
      });
    }
  }, [toast]);

  useEffect(() => {
    if (isLoaded) {
      document.body.style.backgroundImage = `url(${background.url})`;
      document.body.style.backgroundSize = 'cover';
      document.body.style.backgroundPosition = 'center';
      document.body.style.transition = 'background-image 0.5s ease-in-out';
    }
  }, [background.url, isLoaded]);

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background/80 backdrop-blur-sm" data-ai-hint={background.hint}>
      <AppHeader />
      <main className="flex-grow flex flex-col items-center justify-center p-2 md:p-4 landscape:flex-row landscape:gap-4">
        <TimerDisplay />
        <TimerControls />
      </main>
    </div>
  );
}
