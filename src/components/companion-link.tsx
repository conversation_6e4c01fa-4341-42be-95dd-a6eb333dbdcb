'use client';
import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Check, Copy, Share2, Loader2 } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useTimer } from '@/providers/timer-provider';
import { database } from '@/lib/firebase';
import { ref, set, get, update, serverTimestamp } from 'firebase/database';
import { useToast } from '@/hooks/use-toast';

export function CompanionLink({ sessionId }: { sessionId: string }) {
  const { customSessionName, setCustomSessionNameInProvider } = useTimer();
  const { toast } = useToast();

  const [hasCopied, setHasCopied] = useState(false);
  const [origin, setOrigin] = useState('');
  const [localName, setLocalName] = useState(customSessionName || '');
  const [isLoading, setIsLoading] = useState(false);
  const [nameError, setNameError] = useState<string | null>(null);

  useEffect(() => {
    setOrigin(window.location.origin);
  }, []);

  useEffect(() => {
    setLocalName(customSessionName || '');
  }, [customSessionName]);


  const companionUrl = `${origin}/companion/${customSessionName || sessionId}`;

  const handleCopy = () => {
    navigator.clipboard.writeText(companionUrl);
    setHasCopied(true);
    setTimeout(() => setHasCopied(false), 2000);
  };

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setLocalName(value);
    if (nameError) setNameError(null);
  }

  const handleSaveName = async () => {
    if (localName.length < 4) {
      setNameError('Name must be at least 4 characters long.');
      return;
    }
    if (localName === customSessionName) return;


    setIsLoading(true);
    setNameError(null);

    try {
      const nameRef = ref(database, `sessionNames/${localName}`);
      const snapshot = await get(nameRef);

      if (snapshot.exists()) {
        setNameError('This name is already taken. Please choose another.');
      } else {
        await set(nameRef, { sessionId });
        
        const sessionRef = ref(database, `sessions/${sessionId}`);
        await update(sessionRef, { customName: localName });

        setCustomSessionNameInProvider(localName);
        toast({
          title: 'Success!',
          description: `Your shareable link is now active.`,
        });
      }
    } catch (error: any) {
      console.error('Error setting custom name:', error);
      if (error.code === 'PERMISSION_DENIED') {
          setNameError('Permission denied. Please check your database rules.');
          toast({
              title: 'Permission Error',
              description: 'You do not have permission to create a custom session name. Please update your Firebase rules.',
              variant: 'destructive',
              duration: 10000,
          });
      } else {
        setNameError('An error occurred. Please try again.');
         toast({
            title: 'Error',
            description: 'Failed to set custom session name.',
            variant: 'destructive',
        });
      }
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon">
          <Share2 className="h-5 w-5" />
          <span className="sr-only">Share Session</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-80">
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Share Session</h4>
            <p className="text-sm text-muted-foreground">
              Create a custom link for others to follow your timer.
            </p>
          </div>

          <div className='space-y-2'>
            <label htmlFor='customName' className='text-sm font-medium'>Custom Session Name</label>
            <div className="flex items-center space-x-2">
              <Input
                id="customName"
                placeholder="e.g., study-squad"
                value={localName}
                onChange={handleNameChange}
                className={nameError ? 'border-destructive' : ''}
              />
              <Button onClick={handleSaveName} disabled={isLoading || localName.length < 4 || localName === customSessionName}>
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Save'}
              </Button>
            </div>
            {nameError && <p className="text-sm text-destructive">{nameError}</p>}
          </div>
          
          <div className="space-y-2">
             <label htmlFor='shareLink' className='text-sm font-medium'>Your Shareable Link</label>
            <div className="flex items-center space-x-2">
                <Input
                id="shareLink"
                value={companionUrl}
                readOnly
                className="h-9"
                />
                <Button onClick={handleCopy} size="icon" className="h-9 w-9">
                {hasCopied ? (
                    <Check className="h-4 w-4" />
                ) : (
                    <Copy className="h-4 w-4" />
                )}
                </Button>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
