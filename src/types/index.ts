export type ClockStyle = 'digital' | 'analog' | 'vintage' | 'flip';
export type Theme = 'light' | 'dark' | 'system';
export type Language = 'en' | 'es';
export type TimerMode = 'focus' | 'shortBreak' | 'longBreak';
export type ColorPalette = 'default' | 'forest' | 'ocean' | 'sunset' | 'rose';

export interface Settings {
  focusDuration: number;
  shortBreakDuration: number;
  longBreakDuration: number;
  sessionsBeforeLongBreak: number;
  clockStyle: ClockStyle;
  autoStartNextPeriod: boolean;
  backgrounds: {
    focus: { url: string; hint: string };
    shortBreak: { url:string; hint: string };
    longBreak: { url: string; hint: string };
  };
  sounds: {
    focusEnd: string;
    shortBreakEnd: string;
    longBreakEnd: string;
  };
  notifications: {
    enabled: boolean;
  };
  theme: Theme;
  language: Language;
  colorPalette: ColorPalette;
  companionModeEnabled: boolean;
}

export interface SessionRecord {
  id: string;
  type: TimerMode;
  duration: number; // in minutes
  completedAt: string; // ISO string
}

export type CompanionCallStatus = 'calling' | 'busy' | 'ok' | 'no_response';

export interface CompanionCall {
  status: CompanionCallStatus;
  timestamp: number;
}

export type TimerEventType = 'start' | 'pause' | 'skip' | 'addTime' | 'initial';

export interface TimerEvent {
    type: TimerEventType;
    timestamp: number;
    mode?: TimerMode;
    timeLeft?: number;
}

export interface TimerStateData {
    mode: TimerMode;
    timeLeft: number;
    isActive: boolean;
    lastUpdated: number;
    lastEvent: TimerEvent;
    autoStartNextPeriod: boolean;
    call?: CompanionCall;
    customName?: string;
}
