
'use client';

import { createContext, useContext, useEffect, useState, type ReactNode, useCallback, useRef } from 'react';
import type { SessionRecord, TimerMode, TimerStateData, CompanionCall, TimerEvent } from '@/types';
import { useSettings } from './settings-provider';
import * as Tone from 'tone';
import { useLanguage } from './language-provider';
import { HISTORY_KEY } from '@/lib/constants';
import { database, auth } from '@/lib/firebase';
import { ref, onValue, set, onDisconnect, get, update, remove, serverTimestamp } from 'firebase/database';
import { onAuthStateChanged } from 'firebase/auth';
import { CallModal } from '@/components/call-modal';

type TimerProviderState = {
  mode: TimerMode;
  timeLeft: number;
  isActive: boolean;
  sessionCount: number;
  isAutoStarting: boolean;
  isLastTenSeconds: boolean;
  sessionId: string | null;
  customSessionName: string | null;
  isRinging: boolean;
  startTimer: () => void;
  pauseTimer: () => void;
  skipTimer: () => void;
  addMinute: () => void;
  addFiveMinutes: () => void;
  setCustomSessionNameInProvider: (name: string) => void;
};

const TimerProviderContext = createContext<TimerProviderState | undefined>(undefined);

export function TimerProvider({ children }: { children: ReactNode }) {
  const { settings, isLoaded } = useSettings();
  const { t } = useLanguage();
  const [mode, setMode] = useState<TimerMode>('focus');
  const [timeLeft, setTimeLeft] = useState(settings.focusDuration * 60);
  const [isActive, setIsActive] = useState(false);
  const [sessionCount, setSessionCount] = useState(0);
  const [isAutoStarting, setIsAutoStarting] = useState(false);
  const [isLastTenSeconds, setIsLastTenSeconds] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [hasStarted, setHasStarted] = useState(false);
  const [customSessionName, setCustomSessionName] = useState<string | null>(null);
  const [call, setCall] = useState<CompanionCall | null>(null);
  const [isRinging, setIsRinging] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const callTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const setCustomSessionNameInProvider = (name: string) => {
    setCustomSessionName(name);
  };

  const pushEvent = useCallback((event: Omit<TimerEvent, 'timestamp'>) => {
    if (!sessionId || !settings.companionModeEnabled) return;
    const sessionRef = ref(database, `sessions/${sessionId}`);
    
    const eventWithTimestamp: TimerEvent = {
        ...event,
        timestamp: Date.now()
    };

    const dataToUpdate: Partial<TimerStateData> = {
        lastEvent: eventWithTimestamp,
        lastUpdated: serverTimestamp() as unknown as number,
        autoStartNextPeriod: settings.autoStartNextPeriod,
    };

    if (event.type === 'start' || event.type === 'skip' || event.type === 'initial') {
        dataToUpdate.mode = event.mode;
        dataToUpdate.timeLeft = event.timeLeft;
        dataToUpdate.isActive = event.type !== 'initial' ? settings.autoStartNextPeriod || event.type === 'start' : false;
    }
    if (event.type === 'pause') {
        dataToUpdate.timeLeft = event.timeLeft;
        dataToUpdate.isActive = false;
    }
     if (event.type === 'addTime') {
        dataToUpdate.timeLeft = event.timeLeft;
    }
    
    update(sessionRef, dataToUpdate);
  }, [sessionId, settings.autoStartNextPeriod, settings.companionModeEnabled]);

  // Sync autostart setting to Firebase whenever it changes
  useEffect(() => {
    if(sessionId && isLoaded && settings.companionModeEnabled) {
      const sessionRef = ref(database, `sessions/${sessionId}`);
      update(sessionRef, { autoStartNextPeriod: settings.autoStartNextPeriod });
    }
  }, [settings.autoStartNextPeriod, sessionId, isLoaded, settings.companionModeEnabled]);

  useEffect(() => {
    if (!settings.companionModeEnabled) {
        if(sessionId) {
            const sessionRef = ref(database, `sessions/${sessionId}`);
            remove(sessionRef);
            setSessionId(null);
        }
        return;
    };

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user && !sessionId) {
        const newSessionId = user.uid;
        setSessionId(newSessionId);

        const sessionRef = ref(database, `sessions/${newSessionId}`);
        const callRef = ref(database, `sessions/${newSessionId}/call`);
        
        get(sessionRef).then((snapshot) => {
          if (snapshot.exists()) {
            const sessionData: TimerStateData = snapshot.val();
            // Sync from existing session state in Firebase
            const serverTimeLeft = sessionData.timeLeft;
            const lastUpdated = typeof sessionData.lastUpdated === 'number' ? sessionData.lastUpdated : Date.now();
            const timeSinceUpdate = Math.floor((Date.now() - lastUpdated) / 1000);
            
            const correctedTime = sessionData.isActive 
                ? Math.max(0, serverTimeLeft - timeSinceUpdate)
                : serverTimeLeft;

            setMode(sessionData.mode);
            setTimeLeft(correctedTime >= 0 ? correctedTime : 0); // Ensure time is not negative
            setIsActive(sessionData.isActive || false);
            if (sessionData.customName) {
              setCustomSessionName(sessionData.customName);
            }
          } else {
             // Create initial session state in Firebase
            const initialTime = settings.focusDuration * 60; 
            const safeInitialTime = initialTime >= 0 ? initialTime : 0; // Ensure initial time is not negative
            setTimeLeft(safeInitialTime);

            const initialData: TimerStateData = {
                 mode: 'focus',
                 timeLeft: initialTime,
                 isActive: false,
                 autoStartNextPeriod: settings.autoStartNextPeriod,
                 lastUpdated: serverTimestamp() as unknown as number,
                 lastEvent: { type: 'initial', timestamp: Date.now(), mode: 'focus', timeLeft: initialTime }
            }
            set(sessionRef, initialData);
          }
        });
        
        const callUnsubscribe = onValue(callRef, (snapshot) => {
            if (snapshot.exists()) {
                const callData: CompanionCall = snapshot.val();
                if (callData.status === 'calling') {
                    setIsRinging(true);
                    if (navigator.vibrate) {
                      navigator.vibrate([200, 100, 200, 100, 200]);
                    }
                    setCall(callData);

                    if(callTimeoutRef.current) clearTimeout(callTimeoutRef.current);
                    callTimeoutRef.current = setTimeout(() => {
                        handleCallResponse('no_response');
                    }, 11000);
                }
            } else {
                setCall(null);
                setIsRinging(false);
            }
        });

        onDisconnect(sessionRef).remove();

        return () => {
          callUnsubscribe();
        };
      }
    });

    return () => unsubscribe();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId, settings.companionModeEnabled]);

   // Effect to update timer duration when settings change, only for the *current* mode if inactive
   useEffect(() => {
    if (!isLoaded || isActive || hasStarted) return;

    if (mode === 'focus') {
 setTimeLeft(settings.focusDuration * 60 >= 0 ? settings.focusDuration * 60 : 0);
    } else if (mode === 'shortBreak') {
 setTimeLeft(settings.shortBreakDuration * 60 >= 0 ? settings.shortBreakDuration * 60 : 0);
    } else if (mode === 'longBreak') {
 setTimeLeft(settings.longBreakDuration * 60 >= 0 ? settings.longBreakDuration * 60 : 0);
    }
  }, [
    settings.focusDuration, 
    settings.shortBreakDuration, 
    settings.longBreakDuration, 
    isLoaded, 
    mode, 
    isActive, 
    hasStarted
  ]);


  const handleCallResponse = useCallback(async (response: 'busy' | 'ok' | 'no_response') => {
    if (!sessionId || !call || !settings.companionModeEnabled) return;
    if (callTimeoutRef.current) {
        clearTimeout(callTimeoutRef.current);
        callTimeoutRef.current = null;
    }
    setIsRinging(false);

    const callRef = ref(database, `sessions/${sessionId}/call`);
    
    if (response === 'busy' || response === 'ok' || response === 'no_response') {
        const updatedCall: CompanionCall = {
            ...call,
            status: response
        };
        await update(callRef, updatedCall);
        setTimeout(() => {
            remove(callRef);
        }, 5000);

    }
    setCall(null);
  }, [call, sessionId, settings.companionModeEnabled]);


  // Update custom name in Firebase
  useEffect(() => {
    if (sessionId && customSessionName && settings.companionModeEnabled) {
      const sessionRef = ref(database, `sessions/${sessionId}`);
      update(sessionRef, { customName: customSessionName });
    }
  }, [sessionId, customSessionName, settings.companionModeEnabled]);


  const playSound = useCallback((url: string) => {
    try {
      const player = new Tone.Player(url).toDestination();
      Tone.loaded().then(() => player.start());
    } catch (e) { console.error(e) }
  }, []);

  const showNotification = useCallback((title: string, body: string) => {
    if (settings.notifications.enabled && Notification.permission === 'granted') {
      navigator.serviceWorker.ready.then(registration => {
        registration.showNotification(title, { body, icon: '/android-icon-192x192.png' });
      });
    }
  }, [settings.notifications.enabled]);

  const saveSession = useCallback((type: TimerMode, duration: number) => {
    const newSession: SessionRecord = {
      id: new Date().toISOString(),
      type,
      duration,
      completedAt: new Date().toISOString(),
    };
    try {
      const history = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]') as SessionRecord[];
      history.unshift(newSession);
      localStorage.setItem(HISTORY_KEY, JSON.stringify(history.slice(0, 100)));
    } catch (e) { console.error('Failed to save session', e)}
  }, []);

  const nextMode = useCallback((isSkip = false) => {
    setIsActive(false);

    const completedMode = mode;
    const completedDuration =
      completedMode === 'focus' ? settings.focusDuration :
      completedMode === 'shortBreak' ? settings.shortBreakDuration :
      settings.longBreakDuration;
    if (!isSkip) {
        saveSession(completedMode, completedDuration);
    }
    

    let nextModeType: TimerMode;
    let nextDuration: number;

    if (completedMode === 'focus') {
      if(!isSkip) playSound(settings.sounds.focusEnd);
      if(!isSkip) showNotification(t('notification.focus.title'), t('notification.focus.body'));
      const newSessionCount = sessionCount + 1;
      setSessionCount(newSessionCount);
      if (newSessionCount % settings.sessionsBeforeLongBreak === 0) {
        nextModeType = 'longBreak';
        nextDuration = settings.longBreakDuration * 60;
      } else {
        nextModeType = 'shortBreak';
        nextDuration = settings.shortBreakDuration * 60;
      }
    } else {
      if(!isSkip) playSound(settings.sounds.shortBreakEnd);
      if(!isSkip) showNotification(t('notification.break.title'), t('notification.break.body'));
      nextModeType = 'focus';
      nextDuration = settings.focusDuration * 60;
    }

    setMode(nextModeType);
    setTimeLeft(nextDuration);
    setHasStarted(false);

    pushEvent({ type: isSkip ? 'skip' : 'start', mode: nextModeType, timeLeft: nextDuration });

    if (settings.autoStartNextPeriod && !isSkip) {
      setIsAutoStarting(true);
      setTimeout(() => {
        setIsAutoStarting(false);
        setIsActive(true);
        setHasStarted(true);
        pushEvent({ type: 'start', mode: nextModeType, timeLeft: nextDuration });
      }, 1500);
    } else if (isSkip) {
        if (settings.autoStartNextPeriod) {
            setIsActive(true);
            setHasStarted(true);
        }
    }
  }, [mode, sessionCount, settings, t, saveSession, showNotification, playSound, pushEvent]);

  useEffect(() => {
    if (isActive) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            clearInterval(intervalRef.current!);
            setIsLastTenSeconds(false);
            nextMode();
            return 0;
          }
          if (prev <= 10 && prev > 1) {
            setIsLastTenSeconds(true);
          } else if (prev > 10) {
            setIsLastTenSeconds(false);
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [isActive, nextMode]);

  const startTimer = () => {
    Tone.start();
    setIsActive(true);
    setHasStarted(true);
    pushEvent({ type: 'start', mode, timeLeft });
  };
  const pauseTimer = () => {
    setIsActive(false);
    setIsLastTenSeconds(false); 
    pushEvent({ type: 'pause', timeLeft });
  };
  const skipTimer = () => {
    setIsLastTenSeconds(false); 
    nextMode(true);
  };
  const addMinute = () => {
    const newTime = timeLeft + 60;
    setTimeLeft(newTime);
    setIsLastTenSeconds(false); 
    pushEvent({ type: 'addTime', timeLeft: newTime, mode });
  };
  const addFiveMinutes = () => {
    const newTime = timeLeft + 300;
    setTimeLeft(newTime); 
    setIsLastTenSeconds(false);
    pushEvent({ type: 'addTime', timeLeft: newTime, mode });
  };

  const value = { mode, timeLeft, isActive, sessionCount, isAutoStarting, isLastTenSeconds, sessionId, customSessionName, isRinging, startTimer, pauseTimer, skipTimer, addMinute, addFiveMinutes, setCustomSessionNameInProvider };

  return (
    <TimerProviderContext.Provider value={value}>
        {children}
        <CallModal 
            open={!!call && call.status === 'calling'} 
            onBusy={() => handleCallResponse('busy')} 
            onOk={() => handleCallResponse('ok')}
        />
    </TimerProviderContext.Provider>
  );
}

export const useTimer = () => {
  const context = useContext(TimerProviderContext);
  if (context === undefined) {
    throw new Error('useTimer must be used within a TimerProvider');
  }
  return context;
};

    
