
'use client';

import { useEffect, useState, useRef } from 'react';
import { database } from '@/lib/firebase';
import { ref, onValue, set, get } from 'firebase/database';
import { DigitalClock } from '@/components/clocks/digital-clock';
import { useLanguage } from '@/providers/language-provider';
import type { TimerMode, TimerStateData, CompanionCall, TimerEvent } from '@/types';
import { Button } from '@/components/ui/button';
import { BellRing, WifiOff, Maximize, Minimize } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useFullscreen } from '@/providers/fullscreen-provider';
import { cn } from '@/lib/utils';
import { AlertDialog, AlertDialogContent, AlertDialogHeader, AlertDialogTitle, AlertDialogDescription } from '@/components/ui/alert-dialog';
import { useParams } from 'next/navigation';


export default function CompanionPage() {
  const params = useParams();
  const companionId = params?.id as string;
  const { t } = useLanguage();
  const { toast } = useToast();
  const { isFullscreen, toggleFullscreen } = useFullscreen();

  const [mode, setMode] = useState<TimerMode>('focus');
  const [timeLeft, setTimeLeft] = useState<number>(0);
  const [isActive, setIsActive] = useState(false);

  const [isDisconnected, setIsDisconnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [actualSessionId, setActualSessionId] = useState<string | null>(null);
  const [isBellDisabled, setIsBellDisabled] = useState(false);
  const [callResponse, setCallResponse] = useState<CompanionCall | null>(null);
  const [autoStartNextPeriod, setAutoStartNextPeriod] = useState(false);

  const [isResponseModalOpen, setIsResponseModalOpen] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const modeText: Record<TimerMode, string> = {
    focus: t('timer.focus'),
    shortBreak: t('timer.short_break'),
    longBreak: t('timer.long_break'),
  };

  useEffect(() => {
    // Apply system theme
    const root = window.document.documentElement;
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const applyTheme = () => {
        root.classList.remove('light', 'dark');
        root.classList.add(mediaQuery.matches ? 'dark' : 'light');
    };
    
    applyTheme();
    mediaQuery.addEventListener('change', applyTheme);

    return () => mediaQuery.removeEventListener('change', applyTheme);
  }, []);
  
  useEffect(() => {
    if (!companionId) {
        setIsLoading(false);
        setErrorMessage("No session ID provided.");
        return;
    };

    const resolveSessionId = async () => {
        setIsLoading(true);
        const nameRef = ref(database, `sessionNames/${companionId}`);
        const nameSnapshot = await get(nameRef);

        if (nameSnapshot.exists()) {
            const { sessionId } = nameSnapshot.val();
            if(sessionId) {
              setActualSessionId(sessionId);
            } else {
              setErrorMessage("This session name is invalid.");
              setIsLoading(false);
            }
        } else {
            const sessionRef = ref(database, `sessions/${companionId}`);
            const sessionSnapshot = await get(sessionRef);
            if(sessionSnapshot.exists()){
                setActualSessionId(companionId);
            } else {
                 setErrorMessage("This session does not exist or has expired.");
                 setIsLoading(false);
            }
        }
    }

    resolveSessionId();

  }, [companionId]);

  // Effect for handling call notifications
  useEffect(() => {
    if (!actualSessionId) return;

    const callRef = ref(database, `sessions/${actualSessionId}/call`);
    const callUnsubscribe = onValue(callRef, (snapshot) => {
        if (snapshot.exists()) {
            const callData: CompanionCall = snapshot.val();
            setCallResponse(callData);
            if (callData.status === 'busy') {
                setIsBellDisabled(true);
            if(callData.status !== 'calling') {
                setTimeout(() => setCallResponse(null), 5000);
            }
        } else {
            setCallResponse(null);
        }
    });

    return () => callUnsubscribe();
  }, [actualSessionId]);

  // Main effect for handling Firebase connection and events
  useEffect(() => {
    if (!actualSessionId) return;

    const sessionRef = ref(database, `sessions/${actualSessionId}`);
    
    // Initial data fetch
    get(sessionRef).then(snapshot => {
      if (snapshot.exists()) {
        const data: TimerStateData = snapshot.val();
        
        // Sync time based on last update
        const serverTimeLeft = data.timeLeft;
        const lastUpdated = typeof data.lastUpdated === 'number' ? data.lastUpdated : Date.now();
        const timeSinceUpdate = Math.floor((Date.now() - lastUpdated) / 1000);
        
        const correctedTime = data.isActive 
            ? Math.max(0, serverTimeLeft - timeSinceUpdate)
            : serverTimeLeft;

        setMode(data.mode);
        setTimeLeft(correctedTime);
        setIsActive(data.isActive);
        setAutoStartNextPeriod(data.autoStartNextPeriod || false);
        if (data.mode !== 'focus') {
            setIsBellDisabled(false);
        }
        setIsDisconnected(false);
        setErrorMessage(null);
        setIsLoading(false);
      } else {
        setErrorMessage("The session has ended or could not be found.");
        setIsLoading(false);
      }
    });

    // Listener for events and autostart setting
    const sessionDataRef = ref(database, `sessions/${actualSessionId}`);
    const sessionDataUnsubscribe = onValue(sessionDataRef, (snapshot) => {
        if (!snapshot.exists()) return;
        const data: TimerStateData = snapshot.val();

        // Update autostart setting when it changes
        if (data.autoStartNextPeriod !== undefined) {
            setAutoStartNextPeriod(data.autoStartNextPeriod);
        }
    });

    // Listener for events
    const eventRef = ref(database, `sessions/${actualSessionId}/lastEvent`);
    const eventUnsubscribe = onValue(eventRef, (snapshot) => {
        if (!snapshot.exists()) return;
        const event: TimerEvent = snapshot.val();
        if (!event.timestamp) return; // Ignore empty events

        switch (event.type) {
            case 'start':
                setIsActive(true);
                if (event.timeLeft !== undefined) setTimeLeft(event.timeLeft);
                if (event.mode) setMode(event.mode);
                break;
            case 'pause':
                setIsActive(false);
                if (event.timeLeft !== undefined) setTimeLeft(event.timeLeft);
                break;
            case 'skip':
                if (event.mode) setMode(event.mode);
                if (event.timeLeft !== undefined) setTimeLeft(event.timeLeft);
                // Only start the timer if autostart is enabled
                setIsActive(autoStartNextPeriod);
                break;
            case 'addTime':
                if (event.timeLeft !== undefined) setTimeLeft(event.timeLeft);
                break;
        }
         if (event.mode && event.mode !== 'focus') {
            setIsBellDisabled(false); // Re-enable bell on mode change if it was busy
        }
    });


    // Listen for session deletion (disconnect)
    const sessionUnsubscribe = onValue(sessionRef, (snapshot) => {
      if (!snapshot.exists()) {
        setIsActive(false);
        setIsDisconnected(true);
        if (intervalRef.current) clearInterval(intervalRef.current);
      }
    });


    return () => {
        sessionDataUnsubscribe();
        eventUnsubscribe();
        sessionUnsubscribe();
        if (intervalRef.current) clearInterval(intervalRef.current);
    }
  }, [actualSessionId, autoStartNextPeriod]);

  // Local timer interval
  useEffect(() => {
    if (isActive) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
            if (prev <= 1) {
                // The main timer will handle the mode switch and send an event
                // This local timer just stops to avoid going negative
                setIsActive(false);
                return 0;
            }
            return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [isActive]);

  const handleRingBell = async () => {
    if (!actualSessionId || isBellDisabled) return;
    try {
        const callRef = ref(database, `sessions/${actualSessionId}/call`);
        const callData: CompanionCall = {
            status: 'calling',
            timestamp: Date.now()
        };
        await set(callRef, callData); 
        toast({
            title: "Calling...",
            description: "Waiting for a response from your friend.",
        });
    } catch(e: any) {
        console.error("Failed to ring bell", e);
        let description = "Could not ring the bell. Please try again.";
        if (e.code === 'PERMISSION_DENIED') {
            description = "You do not have permission to ring the bell. The session host may need to update their Firebase security rules.";
        }
        toast({
            title: "Error",
            description: description,
            variant: "destructive",
        });
    }
  };

  useEffect(() => {
      if (callResponse && callResponse.status !== 'calling') {
          setIsResponseModalOpen(true);
          const timer = setTimeout(() => {
              setIsResponseModalOpen(false);
          }, 3000); // Close modal after 3 seconds
          return () => clearTimeout(timer);
      }
      setIsResponseModalOpen(false);
  }, [callResponse]);

  const ResponseModal = () => {
    if (!callResponse) return null;

    const messages = {
        busy: { title: "Busy", description: "Your friend is busy right now. The bell is disabled until their next session.", color: "text-red-500" },
        ok: { title: "OK!", description: "Your friend acknowledged the call.", color: "text-green-500" },
        no_response: { title: "No Response", description: "Your friend did not respond.", color: "text-yellow-500" },
    };

    const current = messages[callResponse.status];

    if (!current) return null;

    return (
        <AlertDialog open={isResponseModalOpen}>
            <AlertDialogContent>
                <AlertDialogHeader>
                    <AlertDialogTitle className={cn("text-2xl text-center", current.color)}>
                        {current.title}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-center">
                        {current.description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
            </AlertDialogContent>
        </AlertDialog>
    )
  };


  if (isLoading) {
    return (
        <div className="flex items-center justify-center min-h-screen bg-background">
            <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
            <p className="ml-4 text-lg">Connecting to session...</p>
        </div>
    );
  }

  if (errorMessage) {
     return (
      <div className="flex flex-col min-h-screen items-center justify-center bg-background text-foreground p-4">
        <WifiOff className="h-24 w-24 text-destructive mb-4" />
        <h1 className="text-4xl font-bold mb-2">Session Not Found</h1>
        <p className="text-muted-foreground text-lg text-center">
          {errorMessage}
        </p>
      </div>
    );
  }

  if (isDisconnected) {
    return (
        <div className="flex flex-col min-h-screen items-center justify-center bg-background text-foreground p-4">
            <WifiOff className="h-24 w-24 text-destructive mb-4" />
            <h1 className="text-4xl font-bold mb-2">Session Disconnected</h1>
            <p className="text-muted-foreground text-lg text-center">
            The timer session you were following has ended or the user has disconnected.
            </p>
      </div>
    );
  }

  return (
    <div className="relative flex flex-col min-h-screen items-center justify-center bg-background text-foreground p-4">
        <ResponseModal />
        <div className="absolute top-4 right-4">
            <Button variant="outline" size="icon" onClick={toggleFullscreen} className="bg-background/50 hover:bg-background/80">
                {isFullscreen ? <Minimize className="h-5 w-5" /> : <Maximize className="h-5 w-5" />}
                <span className="sr-only">{isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}</span>
            </Button>
        </div>

      <div className={cn("text-center mb-8", isFullscreen && "scale-110")}>
        <p className="text-2xl font-medium text-foreground tracking-wider uppercase">
          {modeText[mode]}
        </p>
        <p className="text-muted-foreground">Companion Mode</p>
      </div>

      <DigitalClock time={timeLeft} className={cn(isFullscreen && "text-[12rem] lg:text-[15rem]")} />

      <div className="mt-8">
        <Button onClick={handleRingBell} size="lg" disabled={isBellDisabled} className={cn("rounded-full w-24 h-24", isFullscreen && "w-28 h-28")}>
          <BellRing className={cn("w-12 h-12", isFullscreen && "w-14 h-14")} />
          <span className="sr-only">Ring Bell</span>
        </Button>
         {isBellDisabled && <p className="text-xs text-muted-foreground mt-2">Bell disabled by user</p>}
      </div>

       <footer className="absolute bottom-4 text-xs text-muted-foreground">
        Build: {new Date(process.env.NEXT_PUBLIC_BUILD_DATE || 0).toLocaleString()}
      </footer>
    </div>
  );
}
