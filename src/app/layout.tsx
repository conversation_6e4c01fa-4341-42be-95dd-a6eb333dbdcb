import type { Metadata, Viewport } from 'next';
import './globals.css';
import { AppProviders } from '@/providers/app-providers';
import { Toaster } from '@/components/ui/toaster';
import { cn } from '@/lib/utils';
import { APP_NAME } from '@/lib/constants';

export const metadata: Metadata = {
  title: APP_NAME,
  description: 'A Pomodoro timer to help you stay focused and productive.',
  manifest: '/manifest.webmanifest',
};

export const viewport: Viewport = {
  themeColor: '#A7D1AB',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          rel="preconnect"
          href="https://fonts.gstatic.com"
          crossOrigin="anonymous"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap"
          rel="stylesheet"
        />
        <link rel="apple-touch-icon" href="/apple-icon-180x180.png"></link>
      </head>
      <body className={cn('font-body antialiased min-h-screen')}>
        <AppProviders>
          {children}
          <Toaster />
        </AppProviders>
      </body>
    </html>
  );
}
