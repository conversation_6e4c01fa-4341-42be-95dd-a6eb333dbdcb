'use client';

import { useTheme } from '@/providers/theme-provider';
import { useSettings } from '@/providers/settings-provider';
import { useEffect, useState } from 'react';

export default function TestPage() {
  const { theme, colorPalette, setTheme, setColorPalette } = useTheme();
  const { settings, isLoaded } = useSettings();
  const [cssVariables, setCssVariables] = useState<Record<string, string>>({});

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const root = document.documentElement;
      setCssVariables({
        background: getComputedStyle(root).getPropertyValue('--background'),
        primary: getComputedStyle(root).getPropertyValue('--primary'),
        secondary: getComputedStyle(root).getPropertyValue('--secondary'),
        accent: getComputedStyle(root).getPropertyValue('--accent'),
      });
    }
  }, [theme, colorPalette]);

  return (
    <div className="min-h-screen p-8 bg-background text-foreground">
      <h1 className="text-3xl font-bold mb-8 text-primary">Theme Test Page</h1>
      
      <div className="space-y-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Current Settings</h2>
          <div className="bg-card p-4 rounded-lg border">
            <p><strong>Is Loaded:</strong> {isLoaded ? 'Yes' : 'No'}</p>
            <p><strong>Theme:</strong> {theme}</p>
            <p><strong>Color Palette:</strong> {colorPalette}</p>
            <p><strong>Settings:</strong> {JSON.stringify(settings, null, 2)}</p>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Theme Controls</h2>
          <div className="space-x-4 mb-4">
            <button 
              onClick={() => setTheme('light')}
              className="px-4 py-2 bg-primary text-primary-foreground rounded"
            >
              Light
            </button>
            <button 
              onClick={() => setTheme('dark')}
              className="px-4 py-2 bg-primary text-primary-foreground rounded"
            >
              Dark
            </button>
            <button 
              onClick={() => setTheme('system')}
              className="px-4 py-2 bg-primary text-primary-foreground rounded"
            >
              System
            </button>
          </div>
          
          <div className="space-x-4">
            <button 
              onClick={() => setColorPalette('default')}
              className="px-4 py-2 bg-secondary text-secondary-foreground rounded"
            >
              Default
            </button>
            <button 
              onClick={() => setColorPalette('forest')}
              className="px-4 py-2 bg-secondary text-secondary-foreground rounded"
            >
              Forest
            </button>
            <button 
              onClick={() => setColorPalette('ocean')}
              className="px-4 py-2 bg-secondary text-secondary-foreground rounded"
            >
              Ocean
            </button>
            <button 
              onClick={() => setColorPalette('sunset')}
              className="px-4 py-2 bg-secondary text-secondary-foreground rounded"
            >
              Sunset
            </button>
            <button 
              onClick={() => setColorPalette('rose')}
              className="px-4 py-2 bg-secondary text-secondary-foreground rounded"
            >
              Rose
            </button>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">Color Samples</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-background border p-4 rounded">
              <div className="w-full h-8 bg-primary rounded mb-2"></div>
              <p className="text-sm">Primary</p>
            </div>
            <div className="bg-background border p-4 rounded">
              <div className="w-full h-8 bg-secondary rounded mb-2"></div>
              <p className="text-sm">Secondary</p>
            </div>
            <div className="bg-background border p-4 rounded">
              <div className="w-full h-8 bg-accent rounded mb-2"></div>
              <p className="text-sm">Accent</p>
            </div>
            <div className="bg-background border p-4 rounded">
              <div className="w-full h-8 bg-muted rounded mb-2"></div>
              <p className="text-sm">Muted</p>
            </div>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold mb-4">CSS Variables</h2>
          <div className="bg-card p-4 rounded-lg border font-mono text-sm">
            <div>--background: <span className="text-accent">{cssVariables.background || 'Loading...'}</span></div>
            <div>--primary: <span className="text-accent">{cssVariables.primary || 'Loading...'}</span></div>
            <div>--secondary: <span className="text-accent">{cssVariables.secondary || 'Loading...'}</span></div>
            <div>--accent: <span className="text-accent">{cssVariables.accent || 'Loading...'}</span></div>
          </div>
        </div>
      </div>
    </div>
  );
}
