
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');

  if (!query) {
    return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
  }

  const accessKey = process.env.UNSPLASH_ACCESS_KEY;
  if (!accessKey) {
    return NextResponse.json({ error: 'Unsplash API key is not configured' }, { status: 500 });
  }

  const unsplashUrl = `https://api.unsplash.com/search/photos?query=${encodeURIComponent(
    query
  )}&orientation=landscape&per_page=30&client_id=${accessKey}`;

  try {
    const unsplashResponse = await fetch(unsplashUrl);
    if (!unsplashResponse.ok) {
      const errorData = await unsplashResponse.json();
      console.error('Unsplash API error:', errorData);
      return NextResponse.json({ error: 'Failed to fetch image from Unsplash' }, { status: unsplashResponse.status });
    }

    const data = await unsplashResponse.json();
    const imageUrls = data.results.map((photo: { id: string; urls: { regular: string; }; alt_description: string; }) => ({
      id: photo.id,
      url: photo.urls.regular,
      alt: photo.alt_description,
    }));

    return NextResponse.json({ imageUrls });
  } catch (error) {
    console.error('Error fetching from Unsplash:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
