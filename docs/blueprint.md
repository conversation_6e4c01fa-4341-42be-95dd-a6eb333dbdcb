# **App Name**: PestoTimer

## Core Features:

- Timer Display: Display a customizable timer (digital, analog, vintage) with clear indication of focus, short break, and long break intervals.
- Interval Configuration: Configuration section to define focus, short break, and long break intervals.
- Background Image Configuration: Configuration to define background images (via URL) for focus, short break, and long break states, sourced from Unsplash or user provided.
- Timer Controls: Controls for pause, skip, and add-one-minute functionality.
- Audible Alerts: Customizable bell sounds for end-of-period alerts, with different sounds for focus, short break, and long break states.
- Theme Selection: Toggle for dark and light theme, with persistence.
- Push Notifications: Push notifications with sound for end-of-period alerts when the app is in the background.
- Language Support: Multi-language support (English and Spanish), with automatic language selection based on browser locale or defaulting to English.
- Session History: Local tracking of all completed sessions, with basic statistics such as total focus time.

## Style Guidelines:

- Primary color: Soft Green (#A7D1AB) to suggest growth, and concentration, like the herb pesto.
- Background color: Very light green (#F0FAF2) to give a feeling of openness and calm, consistent with productivity apps.
- Accent color: Yellow-Orange (#FFB347) to draw the user's attention and complement the analogous color palette.
- Font: 'Inter' (sans-serif) for a clean, modern, and highly readable interface, suitable for both headlines and body text.
- Use clean, minimalist icons to represent actions and settings.
- A clear, spacious layout, to help improve the usefullness and prevent a sensation of time pressure.
- Subtle, unobtrusive animations on timer start/stop and state transitions.