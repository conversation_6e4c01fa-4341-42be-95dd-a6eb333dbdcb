# Timer +5 Button Feature Test

## Feature Description
When the focus period has 10 seconds left to finish:
1. The +1 button changes to +5 and becomes bigger and pulsating
2. If pressed, the +5 button adds 5 more focus minutes
3. The button resets back to +1 after being pressed
4. If not pressed, it transitions to the next period normally

## Manual Test Steps

### Test 1: Basic +5 Button Appearance
1. Start a focus session
2. Wait until timer reaches 10 seconds remaining
3. **Expected**: +1 button should change to +5 button with:
   - Larger size (w-20 h-20 md:w-24 md:h-24)
   - Orange color scheme (bg-orange-500/20, border-orange-500)
   - Pulsating animation (animate-pulse)
   - +5 icon instead of +1 icon

### Test 2: +5 Button Functionality
1. Start a focus session
2. Wait until timer reaches 10 seconds remaining
3. Click the +5 button
4. **Expected**: 
   - Timer should increase by 5 minutes (300 seconds)
   - But<PERSON> should revert to normal +1 button
   - Timer should continue counting down normally

### Test 3: Natural Transition
1. Start a focus session
2. Wait until timer reaches 10 seconds remaining
3. Do NOT click the +5 button
4. Wait for timer to reach 0
5. **Expected**: 
   - Timer should transition to break period normally
   - +5 button state should be reset

### Test 4: Pause/Resume Behavior
1. Start a focus session
2. Wait until timer reaches 10 seconds remaining
3. Pause the timer
4. **Expected**: +5 button should revert to +1 button
5. Resume the timer
6. **Expected**: Should show +1 button (not +5) since pause resets the state

### Test 5: Skip Behavior
1. Start a focus session
2. Wait until timer reaches 10 seconds remaining
3. Click skip button
4. **Expected**: Should transition to next period and reset +5 button state

## Implementation Details

### Files Modified:
- `src/providers/timer-provider.tsx`: Added `isLastTenSeconds` state and `addFiveMinutes` function
- `src/components/timer-controls.tsx`: Added conditional rendering for +5 button
- `src/components/icons/plus-one.tsx`: Added `PlusFiveIcon` component
- `src/lib/translations.ts`: Added translation keys for +5 minutes

### Key Features:
- State management for last 10 seconds detection
- Conditional button rendering with enhanced styling
- Proper state reset on pause, skip, and time addition
- Internationalization support
- Responsive design considerations
