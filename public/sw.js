
const CACHE_NAME = `pesto-app-cache-v1`;
const PRECACHE_ASSETS = [
  '/',
  '/manifest.webmanifest',
  '/android-icon-192x192.png',
  '/sounds/bell-focus.mp3',
  '/sounds/bell-short.mp3',
  '/sounds/bell-long.mp3'
];


self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(PRECACHE_ASSETS);
      })
      .then(() => {
        self.skipWaiting(); 
      })
  );
});

self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
        return self.clients.claim();
    })
  );
});

self.addEventListener('fetch', (event) => {
    if (event.request.mode === 'navigate') {
        event.respondWith(
            caches.open(CACHE_NAME).then(cache => {
                return cache.match(event.request).then(response => {
                    return response || fetch(event.request).then(fetchResponse => {
                        cache.put(event.request, fetchResponse.clone());
                        return fetchResponse;
                    });
                })
            })
        );
        return;
    }

    event.respondWith(
        caches.match(event.request).then((response) => {
            return response || fetch(event.request).then(fetchResponse => {
                const requestUrl = new URL(event.request.url);
                // Only cache GET requests from our own origin or known CDNs
                if (event.request.method === 'GET' && 
                   (requestUrl.origin === self.location.origin || 
                    requestUrl.hostname.endsWith('unsplash.com') ||
                    requestUrl.hostname.endsWith('gstatic.com') ||
                    requestUrl.hostname.endsWith('googleapis.com'))) {
                    
                    const responseToCache = fetchResponse.clone();
                    caches.open(CACHE_NAME).then(cache => {
                        cache.put(event.request, responseToCache);
                    });
                }
                return fetchResponse;
            }).catch(() => {
                // For images from unsplash, return a placeholder if network fails
                if (event.request.destination === 'image') {
                    return new Response('<svg role="img" aria-labelledby="offline-title" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg"><title id="offline-title">Offline</title><rect width="100%" height="100%" fill="#e5e7eb"></rect><text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-family="sans-serif" font-size="20px" fill="#9ca3af">Image offline</text></svg>', { headers: { 'Content-Type': 'image/svg+xml' } });
                }
            })
        })
    );
});
